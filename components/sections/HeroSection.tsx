'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  Play,
  Pause
} from 'lucide-react';
import { COMPANY_INFO } from '@/lib/constants';
import { useSmoothScroll } from '@/components/ui/InteractiveElements';


interface HeroTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  destination: string;
  featured_image_url: string | null;
}

interface FallbackSlide {
  id: string;
  image_url: string;
}

interface HeroSectionProps {
  heroTrips: HeroTrip[];
}

// Static cursive word component with bold, readable font
const CursiveWord = () => {
  return (
    <motion.span
      className="font-cursive font-bold text-white drop-shadow-2xl text-5xl sm:text-6xl lg:text-7xl xl:text-8xl tracking-wide"
      style={{
        textShadow: '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)',
        fontWeight: 700
      }}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, delay: 0.8 }}
    >
      Dreams
    </motion.span>
  );
};

// Fallback slides if no trips are available - High-quality educational travel images from Cloudinary
const fallbackSlides: FallbackSlide[] = [
  {
    id: 'fallback-1',
    image_url: 'https://res.cloudinary.com/peebst3r/image/upload/v1748960570/positive7/hero-fallbacks/hero-fallback-1.jpg',
  },
  {
    id: 'fallback-2',
    image_url: 'https://res.cloudinary.com/peebst3r/image/upload/v1748960573/positive7/hero-fallbacks/hero-fallback-2.jpg',
  },
  {
    id: 'fallback-3',
    image_url: 'https://res.cloudinary.com/peebst3r/image/upload/v1748960576/positive7/hero-fallbacks/hero-fallback-3.jpg',
  },
  {
    id: 'fallback-4',
    image_url: 'https://res.cloudinary.com/peebst3r/image/upload/v1748960579/positive7/hero-fallbacks/hero-fallback-4.jpg',
  },
];

export default function HeroSection({ heroTrips }: HeroSectionProps) {
  const isFallbackMode = heroTrips.length === 0;
  // Use provided trips or fallback slides
  const slides = isFallbackMode ? fallbackSlides : heroTrips;

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [scrollY, setScrollY] = useState(0);

  // Smooth scroll functionality
  const { scrollToElement } = useSmoothScroll();

  // Parallax effect
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isPlaying, slides.length]);





  // Handle scroll indicator click
  const handleScrollClick = () => {
    // In fallback mode, scroll to about section
    // When there are trips, scroll to featured trips section
    const targetId = isFallbackMode ? 'about-section' : 'featured-trips-section';
    scrollToElement(targetId, 80); // 80px offset for header
  };

  return (
    <section className="relative h-screen min-h-[600px] max-h-[900px] overflow-hidden -mt-16 lg:-mt-20 pt-16 lg:pt-20">
      {isFallbackMode ? (
        /* Scenic background image for fallback mode */
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Mountain landscape with lake"
            fill
            className="object-cover"
            priority
          />
          {/* Dark overlay for text readability */}
          <div className="absolute inset-0 bg-black/50" />
          {/* Subtle animated orbs for depth */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-coral-500/10 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-teal-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
          <div className="absolute top-3/4 left-1/3 w-72 h-72 bg-orange-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }} />
        </div>
      ) : (
        <>
          {/* Animated Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-600/20 via-coral-500/10 to-teal-600/20 animate-gradient-xy" />

          {/* Background Slides with Parallax */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
              className="absolute inset-0"
              style={{
                transform: `translateY(${scrollY * 0.5}px)`,
              }}
            >
              <Image
                src={(slides[currentSlide] as HeroTrip).featured_image_url || '/images/fallback-hero.jpg'}
                alt={(slides[currentSlide] as HeroTrip).title}
                fill
                className="object-cover transition-transform duration-[2000ms] ease-out"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
              <div className="absolute inset-0 bg-gradient-to-r from-primary-900/30 via-transparent to-coral-900/30" />
            </motion.div>
          </AnimatePresence>
        </>
      )}

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-2 h-2 bg-secondary-400 rounded-full animate-float opacity-60" />
        <div className="absolute top-40 right-20 w-3 h-3 bg-coral-400 rounded-full animate-float opacity-40" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-40 left-20 w-1 h-1 bg-teal-400 rounded-full animate-float opacity-80" style={{ animationDelay: '4s' }} />
        <div className="absolute top-60 left-1/3 w-2 h-2 bg-orange-400 rounded-full animate-float opacity-50" style={{ animationDelay: '1s' }} />
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container-custom">
          <div className="max-w-4xl">
            <AnimatePresence mode="wait">
              {isFallbackMode ? (
                <motion.div
                  key="fallback-content"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-white text-center mx-auto max-w-6xl"
                >


                  {/* Single Line Tagline with Proper Spacing */}
                  <motion.div
                    className="mb-16 px-4 sm:px-8 md:px-12 lg:px-16"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                  >
                    <motion.div
                      className="text-center max-w-full mx-auto overflow-visible"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.8, delay: 0.7 }}
                    >
                      {/* Container with fixed height to prevent font cutoff */}
                      <div className="min-h-[100px] sm:min-h-[120px] md:min-h-[140px] lg:min-h-[160px] xl:min-h-[180px] flex items-center justify-center overflow-visible px-4">
                        <motion.h1
                          className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl text-white font-normal leading-relaxed tracking-wide flex flex-wrap items-center justify-center gap-x-2 sm:gap-x-3 md:gap-x-4 lg:gap-x-5 overflow-visible"
                          initial={{ scale: 0.9 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.8, delay: 0.9 }}
                          style={{ lineHeight: '1.3', paddingTop: '0.5rem', paddingBottom: '0.5rem', fontFamily: 'Dancing Script, cursive' }}
                        >
                          <span className="flex-shrink-0">
                            <span className="text-white drop-shadow-2xl">
                              Bring
                            </span>
                          </span>

                          {/* Learning with Morph Hover Effect */}
                          <motion.span
                            className="relative flex-shrink-0 cursor-pointer overflow-visible"
                            whileHover="hover"
                            initial="initial"
                          >
                            {/* Invisible text to maintain consistent width */}
                            <span className="invisible text-white drop-shadow-2xl">
                              Education
                            </span>
                            {/* Actual animated text */}
                            <motion.span
                              className="absolute inset-0 flex items-center justify-center text-white drop-shadow-2xl overflow-visible"
                              variants={{
                                initial: { opacity: 1, filter: "blur(0px)" },
                                hover: {
                                  opacity: 0,
                                  filter: "blur(8px)",
                                  transition: { duration: 0.3 }
                                }
                              }}
                            >
                              Learning
                            </motion.span>
                            <motion.span
                              className="absolute inset-0 flex items-center justify-center text-white drop-shadow-2xl overflow-visible"
                              variants={{
                                initial: { opacity: 0, filter: "blur(8px)" },
                                hover: {
                                  opacity: 1,
                                  filter: "blur(0px)",
                                  transition: { duration: 0.3 }
                                }
                              }}
                            >
                              Education
                            </motion.span>
                          </motion.span>

                          <span className="flex-shrink-0">
                            <span className="text-white drop-shadow-md">
                              to
                            </span>
                          </span>

                          {/* Dreams with Bold Cursive Font */}
                          <span className="flex-shrink-0 overflow-visible">
                            <CursiveWord />
                          </span>
                        </motion.h1>
                      </div>
                    </motion.div>

                    {/* Elegant underline */}
                    <motion.div
                      className="mx-auto mt-8 sm:mt-10 h-1 bg-gradient-to-r from-coral-400 via-orange-400 to-teal-400 rounded-full max-w-md"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 0.8, delay: 1.5, ease: "easeOut" }}
                    />
                  </motion.div>

                  {/* Decorative Elements */}
                  <div className="absolute inset-0 pointer-events-none">
                    <motion.div
                      className="absolute top-[10%] right-[10%] w-3 h-3 sm:w-4 sm:h-4 bg-gradient-to-r from-coral-400 to-orange-400 rounded-full"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.6, 1, 0.6],
                        rotate: [0, 180, 360]
                      }}
                      transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                    />

                    <motion.div
                      className="absolute bottom-[10%] left-[10%] w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-gradient-to-r from-teal-400 to-cyan-400 rounded-full"
                      animate={{
                        scale: [1, 1.3, 1],
                        opacity: [0.4, 0.8, 0.4],
                        rotate: [360, 180, 0]
                      }}
                      transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                    />

                    <motion.div
                      className="absolute top-[65%] right-[25%] w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
                      animate={{
                        scale: [1, 1.8, 1],
                        opacity: [0.5, 1, 0.5],
                        x: [0, 20, 0],
                        y: [0, -10, 0]
                      }}
                      transition={{ duration: 6, repeat: Infinity, ease: "easeInOut", delay: 2 }}
                    />

                    <motion.div
                      className="absolute top-[40%] left-[5%] w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full"
                      animate={{
                        scale: [1, 1.4, 1],
                        opacity: [0.3, 0.7, 0.3],
                        rotate: [0, 270, 360]
                      }}
                      transition={{ duration: 7, repeat: Infinity, ease: "easeInOut", delay: 3 }}
                    />

                    <motion.div
                      className="absolute bottom-[35%] right-[15%] w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gradient-to-r from-emerald-400 to-green-400 rounded-full"
                      animate={{
                        scale: [1, 2, 1],
                        opacity: [0.4, 0.9, 0.4],
                        x: [0, -15, 0],
                        y: [0, 15, 0]
                      }}
                      transition={{ duration: 8, repeat: Infinity, ease: "easeInOut", delay: 4 }}
                    />
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key={currentSlide}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-white"
                >
                  {/* Company Quote */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="mb-6"
                  >
                    <p className="text-lg md:text-xl font-medium text-secondary-300 mb-2">
                      {COMPANY_INFO.heroQuote}
                    </p>
                    <div className="w-20 h-1 bg-secondary-400 rounded"></div>
                  </motion.div>

                  {/* Main Content */}
                  <motion.h1
                    initial={{ opacity: 0, y: 40, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                    className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-display font-black text-white mb-6 leading-tight"
                  >
                    <span className="bg-gradient-to-r from-white via-secondary-200 to-coral-200 bg-clip-text text-transparent">
                      {(slides[currentSlide] as HeroTrip).title}
                    </span>
                  </motion.h1>

                  <motion.h2
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
                    className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-heading font-semibold mb-6"
                  >
                    <span className="bg-gradient-to-r from-secondary-300 to-teal-300 bg-clip-text text-transparent">
                      {(slides[currentSlide] as HeroTrip).destination}
                    </span>
                  </motion.h2>

                  <motion.p
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="text-base sm:text-lg md:text-xl text-gray-100 mb-10 max-w-3xl leading-relaxed font-light"
                  >
                    {(slides[currentSlide] as HeroTrip).description || 'Discover amazing educational experiences with Positive7.'}
                  </motion.p>

                  {/* CTA Buttons */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0 }}
                    className="flex flex-col sm:flex-row gap-4 sm:gap-6"
                  >
                    <Link
                      href={`/trips/${(slides[currentSlide] as HeroTrip).slug}`}
                      className="group relative inline-flex items-center justify-center px-6 sm:px-8 md:px-10 py-3 sm:py-4 md:py-5 bg-gradient-to-r from-primary-600 to-teal-600 text-white font-bold rounded-2xl hover:from-primary-700 hover:to-teal-700 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-primary-500/25 overflow-hidden text-sm sm:text-base"
                    >
                      <div className="absolute inset-0 bg-shimmer-gradient animate-shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <span className="relative z-10">Explore {(slides[currentSlide] as HeroTrip).destination}</span>
                      <ArrowRight className="relative z-10 ml-2 sm:ml-3 h-4 w-4 sm:h-5 sm:w-5 group-hover:translate-x-2 transition-transform duration-300" />
                    </Link>

                    <Link
                      href="/trips"
                      className="group inline-flex items-center justify-center px-6 sm:px-8 md:px-10 py-3 sm:py-4 md:py-5 border-2 border-white/80 text-white font-bold rounded-2xl hover:bg-white/10 backdrop-blur-sm transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-white/10 hover:border-white text-sm sm:text-base"
                    >
                      <span className="group-hover:text-secondary-200 transition-colors duration-300">View All Trips</span>
                    </Link>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Counter and Pause Controls - Only show when not in fallback mode */}
      {!isFallbackMode && slides.length > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.5 }}
          className="absolute bottom-4 sm:bottom-6 md:bottom-8 right-4 sm:right-6 md:right-8 z-20"
        >
          <div className="flex items-center gap-3 bg-black/20 backdrop-blur-md rounded-2xl px-3 sm:px-4 md:px-5 py-2 sm:py-3 border border-white/20">
            {/* Counter */}
            <div className="text-white/90 text-sm sm:text-base font-medium">
              {currentSlide + 1}/{slides.length}
            </div>

            {/* Pause/Play Button */}
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-coral-400/50"
              aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
            >
              {isPlaying ? (
                <Pause className="w-4 h-4 sm:w-5 sm:h-5 text-white/90" />
              ) : (
                <Play className="w-4 h-4 sm:w-5 sm:h-5 text-white/90 ml-0.5" />
              )}
            </button>
          </div>
        </motion.div>
      )}

      {/* Scroll Indicator - Only show in fallback mode */}
      {isFallbackMode && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 2.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center"
        >
          <motion.button
            onClick={handleScrollClick}
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex flex-col items-center text-white/80 hover:text-white transition-colors duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent rounded-lg p-2"
            aria-label="Scroll to next section"
          >
            <span className="text-sm font-light mb-2 tracking-wide">Scroll</span>
            <motion.div
              className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center"
              whileHover={{ scale: 1.1, borderColor: "rgba(255,255,255,0.9)" }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                animate={{ y: [0, 12, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="w-1 h-3 bg-white/60 rounded-full mt-2"
              />
            </motion.div>
          </motion.button>
        </motion.div>
      )}

    </section>
  );
}
