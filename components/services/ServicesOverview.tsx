'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ArrowRight, GraduationCap, Mountain, Users, Calendar, LucideIcon } from 'lucide-react';

interface Service {
  id: string;
  title: string;
  description: string;
  image: string;
  icon: LucideIcon;
  href: string;
  color: string;
  bgColor: string;
  comingSoon?: boolean;
}

const services: Service[] = [
  {
    id: 'edutour',
    title: 'Positive7 EduTour',
    description: 'It is widely agreed that every student should experience the world beyond the classroom. Whatever the subject, with a Student Positive7 you can be sure that learning outcomes will be met and students will be engaged and inspired like never before.',
    image: '/images/fallback-image.jpg',
    icon: GraduationCap,
    href: '/services/edutour',
    color: 'from-blue-500 to-indigo-600',
    bgColor: 'bg-blue-50',
  },
  {
    id: 'adventures',
    title: 'Positive7 Adventures',
    description: 'Using the Camping as a learning tool has huge academic benefits in creating scenarios that participants may never have been presented with or experienced before. It is widely agreed that every student should experience the world beyond the classroom.',
    image: '/images/fallback-image.jpg',
    icon: Mountain,
    href: '/services/adventures',
    color: 'from-green-500 to-emerald-600',
    bgColor: 'bg-green-50',
  },
  {
    id: 'girls-go-solo',
    title: 'Positive7 Girls Go Solo',
    description: 'Girls Go Solo trip means fun, energy, rejuvenation and me time. It\'s something every woman MUST do. We support the idea of women being enough for themselves!',
    image: '/images/fallback-image.jpg',
    icon: Users,
    href: '/services/girls-go-solo',
    color: 'from-pink-500 to-rose-600',
    bgColor: 'bg-pink-50',
  },
  {
    id: 'holidays',
    title: 'Positive7 Holidays',
    description: 'Comprehensive holiday packages designed to create unforgettable memories. From family vacations to group getaways, we craft experiences that bring people together.',
    image: '/images/fallback-image.jpg',
    icon: Calendar,
    href: '/services/holidays',
    color: 'from-orange-500 to-amber-600',
    bgColor: 'bg-orange-50',
    comingSoon: true,
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

export default function ServicesOverview() {
  return (
    <div className="py-16 lg:py-24">
      <div className="container-custom">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
              Our Services
            </span>
          </h1>
          <p className="text-xl lg:text-2xl text-gray-600 font-light italic max-w-3xl mx-auto">
            it's time to see the whole world from a new view
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12"
        >
          {services.map((service) => {
            const IconComponent = service.icon;
            
            return (
              <motion.div
                key={service.id}
                variants={itemVariants}
                className="group relative"
              >
                <Link href={service.href as `/services/${string}`} className="block">
                  <div className={`relative overflow-hidden rounded-2xl ${service.bgColor} p-8 h-full transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] border border-white/50`}>
                    {/* Background Gradient */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />
                    
                    {/* Coming Soon Badge */}
                    {service.comingSoon && (
                      <div className="absolute top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Coming Soon
                      </div>
                    )}

                    {/* Service Image */}
                    <div className="relative h-48 lg:h-56 mb-6 rounded-xl overflow-hidden">
                      <Image
                        src={service.image}
                        alt={service.title}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-500" />
                      
                      {/* Icon Overlay */}
                      <div className={`absolute top-4 left-4 p-3 rounded-full bg-white/90 backdrop-blur-sm`}>
                        <div className={`h-6 w-6 bg-gradient-to-r ${service.color} rounded-sm flex items-center justify-center`}>
                          <IconComponent className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-4">
                      <h3 className="text-2xl font-bold transition-colors duration-300">
                        <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent group-hover:from-primary-700 group-hover:via-secondary-700 group-hover:to-accent-700">
                          {service.title}
                        </span>
                      </h3>
                      
                      <p className="text-gray-600 leading-relaxed line-clamp-4">
                        {service.description}
                      </p>

                      {/* Learn More Button */}
                      <div className="flex items-center text-primary-600 font-medium group-hover:text-primary-700 transition-colors duration-300">
                        <span className="mr-2">Learn More</span>
                        <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-8 border border-white/50">
            <h2 className="text-2xl font-bold mb-4">
              <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                Ready to Start Your Journey?
              </span>
            </h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Whether you're looking for educational experiences, adventure challenges, or personal growth opportunities, 
              we have the perfect service for you.
            </p>
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-3 bg-primary-600 text-white font-medium rounded-full hover:bg-primary-700 transition-colors duration-300 hover:scale-105 transform"
            >
              Get Started Today
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
