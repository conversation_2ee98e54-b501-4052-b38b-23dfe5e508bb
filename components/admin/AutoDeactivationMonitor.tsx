'use client';

import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw, 
  Calendar,
  Settings,
  Play,
  Pause,
  Activity
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import {
  getDeactivationJobStatus,
  triggerTripDeactivation,
  type DeactivationJobStatus
} from '@/lib/utils/trip-deactivation';

interface TripWithAutoDeactivation {
  id: string;
  title: string;
  auto_deactivation_date: string;
  is_active: boolean;
}

export default function AutoDeactivationMonitor() {
  const [jobStatus, setJobStatus] = useState<DeactivationJobStatus[]>([]);
  const [trips, setTrips] = useState<TripWithAutoDeactivation[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [triggering, setTriggering] = useState(false);
  const toast = useToast();

  const fetchData = async () => {
    try {
      const result = await getDeactivationJobStatus();

      if (result.error) {
        toast.error(`Failed to fetch data: ${result.error}`);
      } else {
        setJobStatus(result.jobs);
        setTrips(result.trips);
      }
    } catch (error) {
      console.error('Error fetching auto deactivation data:', error);
      toast.error('Failed to fetch auto deactivation data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    toast.success('Data refreshed successfully');
  };

  const handleTriggerDeactivation = async () => {
    setTriggering(true);
    try {
      const result = await triggerTripDeactivation();
      if (result.error) {
        toast.error(`Failed to trigger deactivation: ${result.error}`);
      } else {
        toast.success(`Successfully processed ${result.count} trips`);
        await fetchData(); // Refresh data after triggering
      }
    } catch (error) {
      console.error('Error triggering deactivation:', error);
      toast.error('Failed to trigger trip deactivation');
    } finally {
      setTriggering(false);
    }
  };



  useEffect(() => {
    fetchData();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const isExpired = (dateString: string) => {
    return new Date(dateString) <= new Date();
  };

  const getTimeUntilDeactivation = (dateString: string) => {
    const now = new Date();
    const deactivationDate = new Date(dateString);
    const diffMs = deactivationDate.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Expired';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
    
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cron Job Status */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-xl">
              <Settings className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Auto Deactivation System</h3>
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleTriggerDeactivation}
              disabled={triggering}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 flex items-center gap-2 text-sm"
            >
              {triggering ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {triggering ? 'Processing...' : 'Trigger Now'}
            </button>

            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2 text-sm"
            >
              {refreshing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Refresh
            </button>
          </div>
        </div>

        {jobStatus.length > 0 ? (
          <div className="space-y-4">
            {jobStatus.map((job) => (
              <div key={job.job_id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${job.active ? 'bg-green-100' : 'bg-red-100'}`}>
                      {job.active ? (
                        <Activity className="w-4 h-4 text-green-600" />
                      ) : (
                        <Pause className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{job.job_name}</h4>
                      <p className="text-sm text-gray-500">
                        Schedule: {job.schedule_natural || job.schedule}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      job.active ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                    }`}>
                      {job.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Last Run:</span>
                    <p className="font-medium">
                      {job.last_run_started_at ? formatDate(job.last_run_started_at) : 'Never'}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-500">Status:</span>
                    <p className={`font-medium ${
                      job.last_run_status === 'succeeded' ? 'text-green-600' :
                      job.last_run_status === 'active' ? 'text-blue-600' : 'text-red-600'
                    }`}>
                      {job.last_run_status || 'Unknown'}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-3" />
            <p className="text-gray-500">No auto deactivation jobs found</p>
          </div>
        )}
      </div>

      {/* Trips with Auto Deactivation */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-orange-100 rounded-xl">
            <Calendar className="w-5 h-5 text-orange-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Trips with Auto Deactivation ({trips.length})
          </h3>
        </div>

        {trips.length > 0 ? (
          <div className="space-y-3">
            {trips.map((trip) => {
              const expired = isExpired(trip.auto_deactivation_date);
              const timeUntil = getTimeUntilDeactivation(trip.auto_deactivation_date);
              
              return (
                <div key={trip.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 mb-1">{trip.title}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>Deactivates: {formatDate(trip.auto_deactivation_date)}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          trip.is_active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                        }`}>
                          {trip.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`flex items-center gap-2 ${
                        expired ? 'text-red-600' : 'text-orange-600'
                      }`}>
                        {expired ? (
                          <AlertTriangle className="w-4 h-4" />
                        ) : (
                          <Clock className="w-4 h-4" />
                        )}
                        <span className="text-sm font-medium">{timeUntil}</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
            <p className="text-gray-500">No trips scheduled for auto deactivation</p>
          </div>
        )}
      </div>
    </div>
  );
}