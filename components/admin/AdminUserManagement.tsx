'use client';

import { useState, memo, useCallback } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import {
  useAdminUsers,
  useAdminRoles,
  useCreateAdminUser,
  useUpdateAdminUser,
  useDeleteAdminUser,
  type AdminUser,
  type Role,
  type CreateUserData,
  type UpdateUserData
} from '@/hooks/useAdminUsers';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

// Types are now imported from the hook

export default function AdminUserManagement() {
  const { hasPermission, isSuperAdmin, isOwner } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingUser, setEditingUser] = useState<AdminUser | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<{ id: string; name: string } | null>(null);

  // React Query hooks
  const {
    data: usersData,
    isLoading: usersLoading,
    error: usersError
  } = useAdminUsers();

  const {
    data: rolesData,
    isLoading: rolesLoading
  } = useAdminRoles();

  const createUserMutation = useCreateAdminUser();
  const updateUserMutation = useUpdateAdminUser();
  const deleteUserMutation = useDeleteAdminUser();

  // Derived data
  const users = usersData?.users || [];
  const roles = rolesData?.roles || [];
  const loading = usersLoading || rolesLoading;
  const error = usersError;

  // Check if user has permission to manage users
  if (!hasPermission('users', 'read')) {
    return (
      <div className="text-center py-8">
        <Shield className="mx-auto h-12 w-12 text-gray-300 mb-4" />
        <p className="text-gray-500">You don't have permission to view admin users.</p>
      </div>
    );
  }

  // Memoized mutation handlers to prevent unnecessary re-renders
  const handleCreateUser = useCallback(async (userData: CreateUserData) => {
    try {
      await createUserMutation.mutateAsync(userData);
      setShowCreateForm(false);
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }, [createUserMutation]);

  const handleUpdateUser = useCallback(async (userId: string, updates: UpdateUserData) => {
    try {
      await updateUserMutation.mutateAsync({ userId, updates });
      setEditingUser(null);
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }, [updateUserMutation]);

  const handleDeleteClick = useCallback((user: AdminUser) => {
    setUserToDelete({
      id: user.id,
      name: user.full_name || user.username || 'Unnamed User'
    });
    setShowDeleteModal(true);
  }, []);

  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    try {
      await deleteUserMutation.mutateAsync(userToDelete.id);
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (error) {
      // Error is handled by the mutation hook with toast
      // Keep modal open on failure so user can retry
      console.error('Failed to delete user:', error);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Users</h1>
          <p className="text-gray-600">Manage admin users and their roles</p>
        </div>
        {hasPermission('users', 'create') && (
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error?.message || 'An error occurred'}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {users.map((user) => (
            <UserListItem
              key={user.id}
              user={user}
              canEdit={isSuperAdmin() || isOwner()}
              onEdit={setEditingUser}
              onDelete={handleDeleteClick}
            />
          ))}
        </ul>
      </div>

      {users.length === 0 && (
        <div className="text-center py-8">
          <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <p className="text-gray-500">No admin users found.</p>
        </div>
      )}

      {/* Create User Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Admin User</h3>
              <CreateUserForm
                roles={roles}
                onSubmit={handleCreateUser}
                onCancel={() => setShowCreateForm(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Edit User Form Modal */}
      {editingUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Edit User</h3>
              <EditUserForm
                user={editingUser}
                roles={roles}
                onSubmit={(updates) => handleUpdateUser(editingUser.id, updates)}
                onCancel={() => setEditingUser(null)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {userToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          title="Delete Admin User"
          message={`Are you sure you want to delete "${userToDelete.name}"?\n\nThis action cannot be undone and will permanently remove the user and revoke their admin access.`}
          confirmText="Delete User"
          variant="danger"
          loading={deleteUserMutation.isPending}
        />
      )}
    </div>
  );
}

// Create User Form Component
function CreateUserForm({
  roles,
  onSubmit,
  onCancel
}: {
  roles: Role[];
  onSubmit: (data: CreateUserData) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    full_name: '',
    username: '',
    role_names: ['content_manager']
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">Email</label>
        <input
          type="email"
          required
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Password</label>
        <input
          type="password"
          required
          value={formData.password}
          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Full Name</label>
        <input
          type="text"
          value={formData.full_name}
          onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Username</label>
        <input
          type="text"
          value={formData.username}
          onChange={(e) => setFormData({ ...formData, username: e.target.value })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Roles</label>
        <select
          multiple
          value={formData.role_names}
          onChange={(e) => setFormData({
            ...formData,
            role_names: Array.from(e.target.selectedOptions, option => option.value)
          })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          {roles.map(role => (
            <option key={role.id} value={role.name}>
              {role.name.replace('_', ' ').toUpperCase()}
            </option>
          ))}
        </select>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          Create User
        </button>
      </div>
    </form>
  );
}

// Edit User Form Component
function EditUserForm({
  user,
  roles,
  onSubmit,
  onCancel
}: {
  user: AdminUser;
  roles: Role[];
  onSubmit: (data: UpdateUserData) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    role_names: user.roles.map(role => role.name),
    is_active: user.is_active
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">User</label>
        <p className="mt-1 text-sm text-gray-900">{user.full_name || user.username}</p>
        <p className="text-sm text-gray-500">{user.id}</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Roles</label>
        <select
          multiple
          value={formData.role_names}
          onChange={(e) => setFormData({
            ...formData,
            role_names: Array.from(e.target.selectedOptions, option => option.value)
          })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          {roles.map(role => (
            <option key={role.id} value={role.name}>
              {role.name.replace('_', ' ').toUpperCase()}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
          <span className="ml-2 text-sm text-gray-700">Active</span>
        </label>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          Update User
        </button>
      </div>
    </form>
  );
}

// Memoized UserListItem component for better performance
const UserListItem = memo(({
  user,
  canEdit,
  onEdit,
  onDelete
}: {
  user: AdminUser;
  canEdit: boolean;
  onEdit: (user: AdminUser) => void;
  onDelete: (user: AdminUser) => void;
}) => {
  const handleEdit = useCallback(() => onEdit(user), [onEdit, user]);
  const handleDelete = useCallback(() => onDelete(user), [onDelete, user]);

  return (
    <li className="px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                <Users className="h-5 w-5 text-gray-600" />
              </div>
            </div>
            <div className="ml-4">
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-900">
                  {user.full_name || user.username || 'Unnamed User'}
                </p>
                {user.is_active ? (
                  <CheckCircle className="ml-2 h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="ml-2 h-4 w-4 text-red-500" />
                )}
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {user.roles.map(role => (
                  <span
                    key={role.id}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {role.name.replace('_', ' ').toUpperCase()}
                  </span>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                Last login: {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : 'Never'}
              </p>
            </div>
          </div>
        </div>
        {canEdit && (
          <div className="flex space-x-2">
            <button
              onClick={handleEdit}
              className="text-blue-600 hover:text-blue-900"
              title="Edit user"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              onClick={handleDelete}
              className="text-red-600 hover:text-red-900"
              title="Delete user"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>
    </li>
  );
});

UserListItem.displayName = 'UserListItem';
