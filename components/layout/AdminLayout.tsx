'use client';

import React, { useState, useEffect } from 'react';
import AdminSidebar from './AdminSidebar';
import { useAdminSmartUpdates } from '@/hooks/useRealTimeUpdates';
import { Menu } from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Enable smart real-time updates for admin panels
  useAdminSmartUpdates();

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile sidebar overlay */}
      <div 
        className={`
          fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity lg:hidden
          ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}
        `}
        onClick={() => setSidebarOpen(false)}
      />

      {/* Sidebar */}
      <div 
        className={`
          fixed inset-y-0 left-0 z-30 w-64 bg-white transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-auto
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}
      >
        <AdminSidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile header with menu button */}
        <header className="bg-white shadow-sm lg:hidden">
          <div className="flex items-center px-4 py-2">
            <button 
              className="p-2 rounded-md hover:bg-gray-100 focus:outline-none"
              onClick={toggleSidebar}
            >
              <Menu className="h-6 w-6 text-gray-600" />
            </button>
            <h1 className="ml-2 text-lg font-semibold text-gray-900">Admin Dashboard</h1>
          </div>
        </header>

        {/* Content area with consistent responsive grid */}
        <main className="flex-1 overflow-auto p-4 sm:p-6">
          <div className="max-w-7xl mx-auto w-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
} 