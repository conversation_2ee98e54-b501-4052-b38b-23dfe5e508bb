import type { <PERSON><PERSON><PERSON>, Viewport } from 'next/types';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>serrat, Dancing_Script, Pacifico } from 'next/font/google';
import './globals.css';
import { COMPANY_INFO } from '@/lib/constants';
import { SecurityHeaders } from '@/components/security/SecurityAccessibility';
import { AriaLiveRegion } from '@/components/security/SecurityAccessibility';
import { SkipToContent } from '@/components/security/SecurityAccessibility';
import { SecurityStatus } from '@/components/security/SecurityAccessibility';
import { PerformanceBudget } from '@/components/performance/PerformanceOptimization';
import { PagePerformanceTracker } from '@/components/performance/PagePerformanceTracker';
import { SplashProvider } from '@/components/providers/SplashProvider';
import AuthProviderWrapper from '@/components/providers/AuthProvider';
import QueryProvider from '@/components/providers/QueryProvider';
import ToastProvider from '@/components/providers/ToastProvider';

import { FloatingAction<PERSON><PERSON>on } from '@/components/ui/InteractiveElements';
import { <PERSON><PERSON><PERSON><PERSON>oundary } from '@/components/error/ErrorBoundary';
import { ComponentManager, DevOnly, BatchComponentLoader } from '@/components/common/ComponentManager';

import Script from 'next/script';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  preload: false,
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800'],
  variable: '--font-poppins',
  display: 'swap',
  preload: false,
});

const montserrat = Montserrat({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-montserrat',
  display: 'swap',
  preload: false,
});

const dancingScript = Dancing_Script({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-dancing-script',
  display: 'swap',
  preload: false,
});

const pacifico = Pacifico({
  subsets: ['latin'],
  weight: ['400'],
  variable: '--font-pacifico',
  display: 'swap',
  preload: false,
});

export const metadata: Metadata = {
  title: {
    default: `${COMPANY_INFO.name} - Educational Tours and Student Travel`,
    template: `%s | ${COMPANY_INFO.name}`,
  },
  description: COMPANY_INFO.description,
  keywords: [
    'educational tours',
    'student travel',
    'experiential learning',
    'adventure camps',
    'school trips',
    'Gujarat tourism',
    'positive7',
    'educational trips',
    'student tours',
    'CAS projects',
    'workshops',
    'picnics',
  ],
  authors: [{ name: COMPANY_INFO.name, url: COMPANY_INFO.website }],
  creator: COMPANY_INFO.name,
  publisher: COMPANY_INFO.name,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(COMPANY_INFO.website),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: COMPANY_INFO.website,
    siteName: COMPANY_INFO.name,
    title: `${COMPANY_INFO.name} - Educational Tours and Student Travel`,
    description: COMPANY_INFO.description,
    images: [
      {
        url: 'https://res.cloudinary.com/peebst3r/image/upload/v1748754558/positive7/general/og-image.jpg',
        width: 1200,
        height: 630,
        alt: `${COMPANY_INFO.name} - Educational Tours & Student Travel`,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: `${COMPANY_INFO.name} - Educational Tours and Student Travel`,
    description: COMPANY_INFO.description,
    images: ['https://res.cloudinary.com/peebst3r/image/upload/v1748754559/positive7/general/twitter-image.jpg'],
    creator: '@positive7ind',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
  category: 'travel',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: '#0ea5e9',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable} ${montserrat.variable} ${dancingScript.variable} ${pacifico.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className="font-sans antialiased">
        {/* Production-ready error handling */}

        {/* Core security and accessibility components */}
        <ComponentManager componentName="SecurityHeaders">
          <SecurityHeaders />
        </ComponentManager>
        <ComponentManager componentName="SkipToContent">
          <SkipToContent />
        </ComponentManager>

        {/* Main application with error boundary */}
        <ComponentManager componentName="ErrorBoundary">
          <ErrorBoundary>
            <ComponentManager componentName="QueryProvider">
              <QueryProvider>
                <ComponentManager componentName="AuthProviderWrapper">
                  <AuthProviderWrapper>
                    <ComponentManager componentName="SplashProvider">
                      <SplashProvider>
                        <PagePerformanceTracker>
                          <div className="flex min-h-screen flex-col" id="main-content">
                            {children}
                          </div>
                        </PagePerformanceTracker>
                      </SplashProvider>
                    </ComponentManager>
                  </AuthProviderWrapper>
                </ComponentManager>
                <ComponentManager componentName="ToastProvider">
                  <ToastProvider />
                </ComponentManager>
                <ComponentManager componentName="FloatingActionButton">
                  <FloatingActionButton />
                </ComponentManager>
              </QueryProvider>
            </ComponentManager>
          </ErrorBoundary>
        </ComponentManager>

        {/* Secondary components with batch loading */}
        <BatchComponentLoader
          components={[
            {
              name: 'AriaLiveRegion',
              component: <AriaLiveRegion />
            },
            {
              name: 'SecurityStatus',
              component: <SecurityStatus />
            },
            {
              name: 'PerformanceBudget',
              component: <PerformanceBudget />
            }
          ]}
        />

        {/* Schema.org structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'TravelAgency',
              name: COMPANY_INFO.name,
              description: COMPANY_INFO.description,
              url: COMPANY_INFO.website,
              logo: `${COMPANY_INFO.website}/images/positive7-logo.png`,
              image: 'https://res.cloudinary.com/peebst3r/image/upload/v1748754558/positive7/general/og-image.jpg',
              telephone: COMPANY_INFO.phone,
              email: COMPANY_INFO.email,
              address: {
                '@type': 'PostalAddress',
                streetAddress: COMPANY_INFO.address,
                addressLocality: 'Ahmedabad',
                addressRegion: 'Gujarat',
                postalCode: '380015',
                addressCountry: 'IN',
              },
              sameAs: [
                'https://www.facebook.com/positive7.ind',
                'https://www.instagram.com/positive.seven/',
                'https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured',
              ],
              serviceType: [
                'Educational Tours',
                'Student Travel',
                'Adventure Camps',
                'Experiential Learning',
                'School Trips',
                'CAS Projects',
                'Workshops',
              ],
              areaServed: {
                '@type': 'Country',
                name: 'India',
              },
            }),
          }}
        />

        {/* PWA Service Worker Registration */}
        <Script id="pwa-registration" strategy="afterInteractive">
          {`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('SW registered: ', registration);
                  })
                  .catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                  });
              });
            }
          `}
        </Script>
      </body>
    </html>
  );
}
