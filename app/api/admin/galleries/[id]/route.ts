import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { verifyAdminAccess } from '@/lib/auth-server';
import { deleteFromCloudinary, deleteCloudinaryFolder } from '@/lib/cloudinary';
import { crud<PERSON>uditLogger, CrudAuditLogger } from '@/lib/crud-audit-logger';
import { measureApiCall } from '@/lib/performance-monitor';

// GET /api/admin/galleries/[id] - Get a single gallery with images
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return measureApiCall('admin_gallery_get', async () => {
    try {
    const { id } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    if (!id) {
      return NextResponse.json(
        { error: 'Gallery ID is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();
    
    // Get gallery with images
    const { data: gallery, error } = await supabase
      .from('galleries')
      .select(`
        *,
        trips:trip_id (
          id,
          title,
          destination
        ),
        gallery_images (
          id,
          image_url,
          cloudinary_public_id,
          order_index,
          created_at,
          updated_at
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching gallery:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Gallery not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch gallery' },
        { status: 500 }
      );
    }

    // Sort images by order_index
    if (gallery.gallery_images) {
      gallery.gallery_images.sort((a: any, b: any) => (a.order_index || 0) - (b.order_index || 0));
    }

    return NextResponse.json({ data: gallery });
  } catch (error) {
    console.error('Error in GET /api/admin/galleries/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
  }, '/api/admin/galleries/[id]');
}

// PUT /api/admin/galleries/[id] - Update a gallery
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return measureApiCall('admin_gallery_update', async () => {
    try {
    const { id } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createServerSupabase();
    const body = await request.json();

    // Get original gallery data for change tracking
    const { data: originalGallery } = await supabase
      .from('galleries')
      .select('*')
      .eq('id', id)
      .single();

    // Basic validation
    const requiredFields = ['name'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Generate folder name from gallery name if not provided
    if (!body.folder_name) {
      body.folder_name = body.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Update gallery in database
    const { data: gallery, error: dbError } = await supabase
      .from('galleries')
      .update(body)
      .eq('id', id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating gallery:', dbError);

      // Log failed update
      await crudAuditLogger.logFailedOperation(
        'update',
        'gallery',
        id,
        user.id,
        user.username || 'Unknown',
        dbError.message,
        request
      );

      if (dbError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Gallery not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to update gallery' },
        { status: 500 }
      );
    }

    // Track changes for audit log
    const changes = originalGallery ? CrudAuditLogger.calculateChanges(originalGallery, body) : {};

    // Log successful update
    await crudAuditLogger.logCrudOperation({
      operation: 'update',
      resourceType: 'gallery',
      resourceId: id,
      resourceTitle: gallery.name,
      userId: user.id,
      userEmail: user.username || 'Unknown',
      changes,
      success: true,
      metadata: {
        updated_at: new Date().toISOString(),
        fields_changed: Object.keys(changes),
      },
    }, request);

    return NextResponse.json({ data: gallery });
  } catch (error) {
    console.error('Error in PUT /api/admin/galleries/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
  }, '/api/admin/galleries/[id]');
}

// DELETE /api/admin/galleries/[id] - Delete a gallery and all its images
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return measureApiCall('admin_gallery_delete', async () => {
    try {
    const { id } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createServerSupabase();

    // First, get gallery details including folder name and name for audit logging
    const { data: gallery, error: galleryError } = await supabase
      .from('galleries')
      .select('name, folder_name')
      .eq('id', id)
      .single();

    if (galleryError) {
      console.error('Error fetching gallery for deletion:', galleryError);
      return NextResponse.json(
        { error: 'Gallery not found' },
        { status: 404 }
      );
    }

    // Get all images to delete from Cloudinary
    const { data: images, error: imagesError } = await supabase
      .from('gallery_images')
      .select('cloudinary_public_id')
      .eq('gallery_id', id);

    if (imagesError) {
      console.error('Error fetching gallery images for deletion:', imagesError);
    }

    // Delete images from Cloudinary in parallel for better performance
    if (images && images.length > 0) {
      const deletionPromises = images.map(async (image) => {
        try {
          await deleteFromCloudinary(image.cloudinary_public_id);
        } catch (cloudinaryError) {
          console.error('Error deleting image from Cloudinary:', cloudinaryError);
          // Continue with database deletion even if Cloudinary deletion fails
        }
      });

      // Wait for all deletions to complete
      await Promise.all(deletionPromises);
    }

    // Delete the entire Cloudinary folder for this gallery
    if (gallery.folder_name) {
      try {
        const folderPath = `positive7/galleries/${gallery.folder_name}`;
        const folderDeleteResult = await deleteCloudinaryFolder(folderPath);
        if (folderDeleteResult.success) {
          console.log(`Successfully deleted Cloudinary folder: ${folderPath}`);
        } else {
          console.warn(`Failed to delete Cloudinary folder: ${folderPath}`, folderDeleteResult.error);
        }
      } catch (folderError) {
        console.error('Error deleting Cloudinary folder:', folderError);
        // Continue with database deletion even if folder deletion fails
      }
    }

    // Delete gallery from database (this will cascade delete images due to foreign key constraint)
    const { error: deleteError } = await supabase
      .from('galleries')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting gallery:', deleteError);

      // Log failed deletion
      await crudAuditLogger.logFailedOperation(
        'delete',
        'gallery',
        id,
        user.id,
        user.username || 'Unknown',
        deleteError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to delete gallery' },
        { status: 500 }
      );
    }

    // Log successful deletion
    await crudAuditLogger.logCrudOperation({
      operation: 'delete',
      resourceType: 'gallery',
      resourceId: id,
      resourceTitle: gallery.name,
      userId: user.id,
      userEmail: user.username || 'Unknown',
      success: true,
      metadata: {
        deleted_at: new Date().toISOString(),
        deleted_gallery_name: gallery.name,
        cloudinary_folder_deleted: !!gallery.folder_name,
        images_count: images?.length || 0,
      },
    }, request);

    return NextResponse.json({
      message: 'Gallery deleted successfully',
      cloudinaryFolderDeleted: !!gallery.folder_name
    });
  } catch (error) {
    console.error('Error in DELETE /api/admin/galleries/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
  }, '/api/admin/galleries/[id]');
}
