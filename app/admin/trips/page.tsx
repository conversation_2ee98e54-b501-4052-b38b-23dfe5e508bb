'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { Trip, TripDifficulty } from '@/types/trip';
import AdminLayout from '@/components/layout/AdminLayout';
import { Edit, Trash2, Plus } from 'lucide-react';
import { useTrips, useDeleteTrip, useToggleTripStatus, useToggleTripFeatured, TripQueryParams } from '@/hooks/useTrips';
import { useToast } from '@/hooks/useToast';
import { useDebounce } from '@/hooks/useDebounce';
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const DIFFICULTY_LEVELS: TripDifficulty[] = ['easy', 'moderate', 'challenging', 'extreme'];

export default function TripsManagementPage() {
  const router = useRouter();
  const toast = useToast();

  // Filters
  const [search, setSearch] = useState('');
  const [destination, setDestination] = useState('all');
  const [difficulty, setDifficulty] = useState('all');
  const [isActive, setIsActive] = useState<boolean | null>(null);
  const [page, setPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [tripToDelete, setTripToDelete] = useState<{ id: string; title: string } | null>(null);
  const limit = 10;

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300);

  // Use modern hooks with debounced search
  const queryParams: TripQueryParams = {
    page,
    limit,
    search: debouncedSearch || undefined,
    destination: destination !== 'all' ? destination : undefined,
    difficulty: difficulty !== 'all' ? (difficulty as TripDifficulty) : undefined,
    isActive: isActive === null ? undefined : String(isActive),
  };

  const { data, isLoading, error } = useTrips(queryParams);
  const deleteTrip = useDeleteTrip();
  const toggleTripStatus = useToggleTripStatus();
  const toggleTripFeatured = useToggleTripFeatured();

  const handleDeleteClick = (trip: Trip) => {
    setTripToDelete({ id: trip.id, title: trip.title });
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!tripToDelete) return;

    try {
      await deleteTrip.mutateAsync(tripToDelete.id);
      setShowDeleteModal(false);
      setTripToDelete(null);
      // Success toast is handled by the hook
    } catch (err) {
      // Error toast is handled by the hook
      console.error('Error deleting trip:', err);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setTripToDelete(null);
  };

  const handleToggleStatus = async (trip: Trip) => {
    const toastId = toast.loading(trip.is_active ? 'Deactivating trip...' : 'Activating trip...');

    try {
      await toggleTripStatus.mutateAsync({
        id: trip.id,
        isActive: !trip.is_active
      });
      toast.success(trip.is_active ? 'Trip deactivated successfully' : 'Trip activated successfully');
    } catch (error) {
      console.error('Error updating trip status:', error);
      toast.error('Failed to update trip status');
    } finally {
      toast.dismiss(toastId);
    }
  };

  const handleToggleFeatured = async (trip: Trip) => {
    const toastId = toast.loading(trip.is_featured ? 'Removing from featured...' : 'Adding to featured...');

    try {
      await toggleTripFeatured.mutateAsync({
        id: trip.id,
        isFeatured: !trip.is_featured
      });
      toast.success(trip.is_featured ? 'Trip removed from featured successfully' : 'Trip added to featured successfully');
    } catch (error) {
      console.error('Error updating trip featured status:', error);
      toast.error('Failed to update featured status');
    } finally {
      toast.dismiss(toastId);
    }
  };

  const trips = data?.data || [];
  const pagination = data?.pagination || { page: 1, limit: 10, total: 0, totalPages: 0 };

  return (
    <PageErrorBoundary context="admin-trips-page">
      <AdminLayout>
        <SectionErrorBoundary context="admin-trips-header">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Trip Management</h1>
            <Button
              onClick={() => router.push('/admin/trips/new' as any)}
              className="flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add New Trip
            </Button>
          </div>
        </SectionErrorBoundary>

        {/* Filters */}
        <SectionErrorBoundary context="admin-trips-filters">
          <div className="bg-white p-4 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  placeholder="Search trips..."
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Destination
                </label>
                <select
                  value={destination}
                  onChange={(e) => setDestination(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="all">All Destinations</option>
                  {/* Add destinations dynamically if needed */}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Difficulty
                </label>
                <select
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="all">All Difficulties</option>
                  {DIFFICULTY_LEVELS.map(level => (
                    <option key={level} value={level}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={isActive === null ? '' : String(isActive)}
                  onChange={(e) => setIsActive(e.target.value === '' ? null : e.target.value === 'true')}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">All</option>
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
          </div>
        </SectionErrorBoundary>

        {/* Error message */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error?.message || 'An error occurred'}</p>
              </div>
            </div>
          </div>
        )}

        {/* Trips table */}
        <SectionErrorBoundary context="admin-trips-table">
          <div className="bg-white shadow rounded-lg overflow-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Destination
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Featured
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center">
                  <LoadingSpinner size="medium" />
                </td>
              </tr>
            ) : trips.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                  No trips found
                </td>
              </tr>
            ) : (
              trips.map((trip) => (
                <tr key={trip.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {trip.title}
                    </div>
                    {trip.description && (
                      <div className="text-sm text-gray-500 truncate max-w-md">
                        {trip.description}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {trip.destination}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleToggleStatus(trip)}
                      className="flex items-center focus:outline-none"
                      aria-pressed={trip.is_active === true}
                      title={trip.is_active ? "Deactivate" : "Activate"}
                      disabled={toggleTripStatus.isPending}
                    >
                      <span
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${
                          trip.is_active ? 'bg-green-500' : 'bg-gray-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${
                            trip.is_active ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </span>
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleToggleFeatured(trip)}
                      className="flex items-center focus:outline-none"
                      aria-pressed={trip.is_featured === true}
                      title={trip.is_featured ? "Remove from featured" : "Add to featured"}
                      disabled={toggleTripFeatured.isPending}
                    >
                      <span
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${
                          trip.is_featured ? 'bg-yellow-500' : 'bg-gray-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${
                            trip.is_featured ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </span>
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link
                        href={`/admin/trips/${trip.id}/edit` as any}
                        className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-gray-100"
                        title="Edit Trip"
                      >
                        <Edit className="h-5 w-5" />
                      </Link>
                      <button
                        onClick={() => handleDeleteClick(trip)}
                        className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-gray-100"
                        title="Delete Trip"
                        disabled={deleteTrip.isPending}
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
          </div>
        </SectionErrorBoundary>

        {/* Pagination */}
        {!isLoading && pagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> to{' '}
              <span className="font-medium">
                {Math.min(pagination.page * pagination.limit, pagination.total)}
              </span>{' '}
              of <span className="font-medium">{pagination.total}</span> results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPage(page - 1)}
                disabled={pagination.page === 1}
                className="px-3 py-1 border rounded-md disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setPage(page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className="px-3 py-1 border rounded-md disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {tripToDelete && (
          <ConfirmationModal
            isOpen={showDeleteModal}
            onClose={handleDeleteCancel}
            onConfirm={handleDeleteConfirm}
            title="Delete Trip"
            message={`Are you sure you want to delete "${tripToDelete.title}"?\n\nThis action cannot be undone and will permanently remove the trip and all associated data.`}
            confirmText="Delete Trip"
            variant="danger"
            loading={deleteTrip.isPending}
          />
        )}
      </AdminLayout>
    </PageErrorBoundary>
  );
}